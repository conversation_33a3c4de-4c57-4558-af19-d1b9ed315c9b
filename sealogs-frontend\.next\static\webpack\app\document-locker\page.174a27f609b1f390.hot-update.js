"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/document-locker/page",{

/***/ "(app-pages-browser)/./src/app/ui/document-locker/list.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/document-locker/list.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocumentList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsDocumentLockerIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/icons/SealogsDocumentLockerIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsDocumentLockerIcon.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DocumentList() {\n    _s();\n    const [vesselListWithDocuments, setVesselListWithDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [vesselListWithDocumentsCopy, setVesselListWithDocumentsCopy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryVesselListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.VESSEL_LIST_WITH_DOCUMENTS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readVessels.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: element.__typename,\n                            type_title: element.title,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const [queryInventoriesListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_INVENTORIES_WITH_DOCUMENTS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readInventories.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: element.__typename,\n                            type_title: element.title,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const [queryMaintenanceListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.MAINTENANCE_LIST_WITH_DOCUMENT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readComponentMaintenanceChecks.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: \"Maintenance\",\n                            type_title: element.name,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        let searchFilter = {\n            ...filter\n        };\n        let updatedVesselList = vesselListWithDocumentsCopy;\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.vesselID = {\n                    in: data.map((item)=>+item.value)\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>searchFilter.vesselID.in.includes(+item.type_id));\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.vesselID = {\n                    eq: +data.value\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>item.type_id === data.value.toString());\n            } else {\n                delete searchFilter.vesselID;\n            }\n        }\n        if (type === \"Module\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.moduleName = {\n                    in: data.map((item)=>item.value)\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>searchFilter.moduleName.in.includes(item.type));\n            } else if (data && data.value && !Array.isArray(data)) {\n                searchFilter.moduleName = data.value;\n                updatedVesselList = updatedVesselList.filter((item)=>item.type === data.value);\n            } else {\n                delete searchFilter.moduleName;\n            }\n        }\n        if (type === \"keyword\") {\n            var _data_value;\n            if (data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim()) {\n                const lowerCaseContains = data.value.trim().toLowerCase();\n                searchFilter.item = {\n                    contains: lowerCaseContains\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>{\n                    const lowerCaseTitle = item.title.toLowerCase();\n                    const lowerCaseName = item.name.toLowerCase();\n                    return lowerCaseTitle.includes(lowerCaseContains) || lowerCaseName.includes(lowerCaseContains);\n                });\n            } else {\n                delete searchFilter.item;\n            }\n        }\n        setFilter(searchFilter);\n        setVesselListWithDocuments(updatedVesselList);\n    };\n    const [readOneClient] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.READ_ONE_CLIENT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneClient;\n            if (data) {\n                const docs = data.documents.nodes.map((doc)=>{\n                    return {\n                        ...doc,\n                        type: \"Company\",\n                        type_title: \"\",\n                        type_id: 0\n                    };\n                });\n                if (docs.length > 0) {\n                    setVesselListWithDocuments([\n                        ...vesselListWithDocuments,\n                        ...docs\n                    ]);\n                    setVesselListWithDocumentsCopy([\n                        ...vesselListWithDocuments,\n                        ...docs\n                    ]);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readOneClient error\", error);\n        }\n    });\n    const loadClientDocuments = async ()=>{\n        var _localStorage_getItem;\n        await readOneClient({\n            variables: {\n                filter: {\n                    id: {\n                        eq: +((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0)\n                    }\n                }\n            }\n        });\n    };\n    const loadVesselDocuments = async ()=>{\n        await queryVesselListDocument({\n            variables: {\n                filter: {\n                    archived: {\n                        eq: false\n                    }\n                }\n            }\n        });\n    };\n    const loadInventoryDocuments = async ()=>{\n        await queryInventoriesListDocument();\n    };\n    const loadMaintenanceDocuments = async ()=>{\n        await queryMaintenanceListDocument();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselDocuments();\n            loadInventoryDocuments();\n            loadMaintenanceDocuments();\n            loadClientDocuments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.createColumns)([\n        {\n            accessorKey: \"name\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1 py-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://api.sealogs.com/assets/\" + document.fileFilename,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"font-medium hover:underline flex items-center gap-1\",\n                                children: [\n                                    document.name,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 25\n                        }, this),\n                        document.type && document.type_title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.P, {\n                                    className: \"text-xs\",\n                                    children: [\n                                        document.type,\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 33\n                                }, this),\n                                document.type_title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: document.type_title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex it phablet:hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.P, {\n                                    className: \"text-xs\",\n                                    children: \"Created: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 29\n                                }, this),\n                                (document === null || document === void 0 ? void 0 : document.created) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(document.created) : \"No Date\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"type\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Module\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: document.type\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 24\n                }, this);\n            },\n            breakpoint: \"tablet-md\",\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.type) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.type) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"type_title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Item\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block text-sm\",\n                    children: !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(document.type_title) ? document.type_title : \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.type_title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.type_title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"created\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Upload date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            breakpoint: \"phablet\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block text-sm\",\n                    children: (document === null || document === void 0 ? void 0 : document.created) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(document.created) : \"No Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.created) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.created) || 0).getTime();\n                return dateB - dateA;\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_8__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsDocumentLockerIcon__WEBPACK_IMPORTED_MODULE_9__.SealogsDocumentLockerIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 21\n                }, void 0),\n                title: \"Document locker\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                lineNumber: 373,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n                    columns: columns,\n                    data: vesselListWithDocuments,\n                    showToolbar: true,\n                    pageSize: 20,\n                    onChange: handleFilterOnChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                lineNumber: 379,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DocumentList, \"0Dd6wy81uhwY94XofMSLucfN7Jw=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = DocumentList;\nvar _c;\n$RefreshReg$(_c, \"DocumentList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/document-locker/list.tsx\n"));

/***/ })

});