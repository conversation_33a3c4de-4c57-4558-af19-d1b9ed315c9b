"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/document-locker/page",{

/***/ "(app-pages-browser)/./src/app/ui/document-locker/list.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/document-locker/list.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocumentList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsDocumentLockerIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/icons/SealogsDocumentLockerIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsDocumentLockerIcon.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DocumentList() {\n    _s();\n    const [vesselListWithDocuments, setVesselListWithDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [vesselListWithDocumentsCopy, setVesselListWithDocumentsCopy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryVesselListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.VESSEL_LIST_WITH_DOCUMENTS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readVessels.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: element.__typename,\n                            type_title: element.title,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const [queryInventoriesListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_INVENTORIES_WITH_DOCUMENTS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readInventories.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: element.__typename,\n                            type_title: element.title,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const [queryMaintenanceListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.MAINTENANCE_LIST_WITH_DOCUMENT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readComponentMaintenanceChecks.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: \"Maintenance\",\n                            type_title: element.name,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        let searchFilter = {\n            ...filter\n        };\n        let updatedVesselList = vesselListWithDocumentsCopy;\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.vesselID = {\n                    in: data.map((item)=>+item.value)\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>searchFilter.vesselID.in.includes(+item.type_id));\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.vesselID = {\n                    eq: +data.value\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>item.type_id === data.value.toString());\n            } else {\n                delete searchFilter.vesselID;\n            }\n        }\n        if (type === \"Module\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.moduleName = {\n                    in: data.map((item)=>item.value)\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>searchFilter.moduleName.in.includes(item.type));\n            } else if (data && data.value && !Array.isArray(data)) {\n                searchFilter.moduleName = data.value;\n                updatedVesselList = updatedVesselList.filter((item)=>item.type === data.value);\n            } else {\n                delete searchFilter.moduleName;\n            }\n        }\n        if (type === \"keyword\") {\n            var _data_value;\n            if (data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim()) {\n                const lowerCaseContains = data.value.trim().toLowerCase();\n                searchFilter.item = {\n                    contains: lowerCaseContains\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>{\n                    const lowerCaseTitle = item.title.toLowerCase();\n                    const lowerCaseName = item.name.toLowerCase();\n                    return lowerCaseTitle.includes(lowerCaseContains) || lowerCaseName.includes(lowerCaseContains);\n                });\n            } else {\n                delete searchFilter.item;\n            }\n        }\n        setFilter(searchFilter);\n        setVesselListWithDocuments(updatedVesselList);\n    };\n    const [readOneClient] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.READ_ONE_CLIENT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneClient;\n            if (data) {\n                const docs = data.documents.nodes.map((doc)=>{\n                    return {\n                        ...doc,\n                        type: \"Company\",\n                        type_title: \"\",\n                        type_id: 0\n                    };\n                });\n                if (docs.length > 0) {\n                    setVesselListWithDocuments([\n                        ...vesselListWithDocuments,\n                        ...docs\n                    ]);\n                    setVesselListWithDocumentsCopy([\n                        ...vesselListWithDocuments,\n                        ...docs\n                    ]);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readOneClient error\", error);\n        }\n    });\n    const loadClientDocuments = async ()=>{\n        var _localStorage_getItem;\n        await readOneClient({\n            variables: {\n                filter: {\n                    id: {\n                        eq: +((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0)\n                    }\n                }\n            }\n        });\n    };\n    const loadVesselDocuments = async ()=>{\n        await queryVesselListDocument({\n            variables: {\n                filter: {\n                    archived: {\n                        eq: false\n                    }\n                }\n            }\n        });\n    };\n    const loadInventoryDocuments = async ()=>{\n        await queryInventoriesListDocument();\n    };\n    const loadMaintenanceDocuments = async ()=>{\n        await queryMaintenanceListDocument();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselDocuments();\n            loadInventoryDocuments();\n            loadMaintenanceDocuments();\n            loadClientDocuments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.createColumns)([\n        {\n            accessorKey: \"name\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://api.sealogs.com/assets/\" + document.fileFilename,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"font-medium hover:underline flex items-center gap-1\",\n                                children: [\n                                    document.name,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 25\n                        }, this),\n                        document.type && document.type_title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.P, {\n                                    className: \"text-xs\",\n                                    children: [\n                                        document.type,\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 33\n                                }, this),\n                                document.type_title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: document.type_title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block md:hidden text-sm text-muted-foreground\",\n                            children: [\n                                \"Created:\",\n                                \" \",\n                                (document === null || document === void 0 ? void 0 : document.created) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(document.created) : \"No Date\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"type\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Module\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: document.type\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 24\n                }, this);\n            },\n            breakpoint: \"tablet-md\",\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.type) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.type) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"type_title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Item\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block text-sm\",\n                    children: !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(document.type_title) ? document.type_title : \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.type_title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.type_title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"created\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Upload date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            breakpoint: \"phablet\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block text-sm\",\n                    children: (document === null || document === void 0 ? void 0 : document.created) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(document.created) : \"No Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.created) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.created) || 0).getTime();\n                return dateB - dateA;\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_8__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsDocumentLockerIcon__WEBPACK_IMPORTED_MODULE_9__.SealogsDocumentLockerIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 21\n                }, void 0),\n                title: \"Document locker\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                lineNumber: 377,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n                    columns: columns,\n                    data: vesselListWithDocuments,\n                    showToolbar: true,\n                    pageSize: 20,\n                    onChange: handleFilterOnChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                lineNumber: 383,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DocumentList, \"0Dd6wy81uhwY94XofMSLucfN7Jw=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = DocumentList;\nvar _c;\n$RefreshReg$(_c, \"DocumentList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/document-locker/list.tsx\n"));

/***/ })

});